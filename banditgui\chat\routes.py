"""
Chat-related routes for BanditGUI.
"""

import os

from flask import Blueprint, jsonify, request
from litellm import completion  # Assuming litellm is installed

from banditgui.config.logging import get_logger
from banditgui.utils.decorators import api_error_handler
from banditgui.utils.llm_utils import construct_ask_a_pro_prompt

# Initialize logger for this blueprint
chat_bp_logger = get_logger('chat.routes')

# Create a Blueprint
chat_bp = Blueprint('chat', __name__, url_prefix='/chat')

# This will be set by the main app when the blueprint is registered
chat_manager = None

@chat_bp.record_once
def record_managers(state):
    """
    Callback to record manager instances from the main app.
    """
    global chat_manager
    if 'chat_manager' in state.app.config:
        chat_manager = state.app.config['chat_manager']
    chat_bp_logger.debug("Chat Blueprint managers recorded.")


@chat_bp.route('/message', methods=['POST'])
@api_error_handler
def chat_message():
    """Add a message to the chat."""
    if chat_manager is None:
        chat_bp_logger.error("ChatManager not initialized in blueprint.")
        return jsonify({"status": "error", "message": "Server component not ready."}), 500

    message = request.json.get('message')
    level = request.json.get('level')
    try:
        level = int(level)
    except (ValueError, TypeError):
        chat_bp_logger.warning("Invalid level value provided in chat message")
        return jsonify({'status': 'error', 'message': 'Invalid level value'})
    is_system = request.json.get('isSystem', False)

    if not message:
        chat_bp_logger.warning("Chat message request with no message")
        return jsonify({'status': 'error', 'message': 'No message provided'})

    chat_manager.add_message(message, level, is_system)
    chat_bp_logger.info(f"Chat message added: {message[:50]}...")
    return jsonify({'status': 'success', 'message': 'Message added'})


@chat_bp.route('/messages', methods=['GET'])
@api_error_handler
def get_chat_messages():
    """Get all chat messages."""
    if chat_manager is None:
        chat_bp_logger.error("ChatManager not initialized in blueprint.")
        return jsonify({"status": "error", "message": "Server component not ready."}), 500

    chat_bp_logger.debug("Fetching all chat messages")
    messages = chat_manager.get_messages()
    return jsonify({'status': 'success', 'messages': messages})


@chat_bp.route('/hint', methods=['POST'])
@api_error_handler
def get_hint():
    """Get a hint for the current level from the LLM."""
    if chat_manager is None:
        chat_bp_logger.error("ChatManager not initialized in blueprint.")
        return jsonify({"status": "error", "message": "Server component not ready."}), 500

    level = request.json.get('level')
    if level is None:
        chat_bp_logger.warning("Hint request missing level information.")
        return jsonify({'status': 'error', 'message': 'Level information required for hint.'}), 400

    try:
        hint = chat_manager.get_hint(level)
        chat_bp_logger.info(f"Generated hint for level {level}: {hint[:50]}...")
        return jsonify({'status': 'success', 'hint': hint})
    except Exception as e:
        chat_bp_logger.error(f"Error generating hint for level {level}: {e}")
        return jsonify({'status': 'error', 'message': f'Error generating hint: {e}'}), 500


@chat_bp.route('/ask-a-pro', methods=['POST'])
@api_error_handler
def ask_a_pro():
    data = request.json
    selected_llm_value = data.get('llm') # e.g., "openai/gpt-4o"
    level_name = data.get('level_name')
    level_description = data.get('level_description')
    command_history_list = data.get('command_history', [])

    # Basic validation
    if not all([selected_llm_value, level_name is not None, level_description]): # level_name can be 0
        chat_bp_logger.warning("Ask-a-Pro request missing required data.")
        return jsonify({'status': 'error', 'message': 'Missing required data for Ask-a-Pro.'}), 400

    # Construct the prompt using the helper function
    prompt = construct_ask_a_pro_prompt(
        level_name,
        level_description,
        command_history_list
    )

    try:
        # Ensure we have the correct API key set for the selected LLM provider
        provider = selected_llm_value.split('/')[0]
        
        # Default model_name_for_api to the full value, adjust as needed
        model_name_for_api = selected_llm_value 

        api_key = None
        # API Key resolution based on provider
        if provider == "openai":
            api_key = os.getenv("OPENAI_API_KEY")
            # model_name_for_api is already correct e.g. "openai/gpt-4o" or "gpt-4o" if litellm handles it
        elif provider == "gemini":
            model_name_for_api = selected_llm_value # e.g. "gemini/gemini-1.5-pro"
            api_key = os.getenv("GEMINI_API_KEY")
        elif provider == "anthropic":
            api_key = os.getenv("ANTHROPIC_API_KEY")
            # model_name_for_api for anthropic is often just the model name e.g. "claude-3-opus-20240229"
            # but llm_model.json has "claude-3-opus", litellm might prepend "anthropic/" or handle it.
            # For safety, pass the full selected_llm_value if it includes provider, else construct.
        elif provider == "cohere":
            api_key = os.getenv("COHERE_API_KEY")
        elif provider == "mistral":
            api_key = os.getenv("MISTRAL_API_KEY")
        elif provider == "perplexity":
            api_key = os.getenv("PERPLEXITY_API_KEY")
        elif provider == "deepseek":
            api_key = os.getenv("DEEPSEEK_API_KEY")
        elif provider == "groq":
            api_key = os.getenv("GROQ_API_KEY")
        elif provider == "openrouter":
            api_key = os.getenv("OPENROUTER_API_KEY")
        elif provider == "ollama":
            # OLLAMA_BASE_URL should be set in .env for LiteLLM to pick up.
            # api_key is typically not needed for local Ollama.
            pass

        # Check for API key after specific provider logic
        if not api_key and provider not in ["ollama"] and not os.getenv(f"{provider.upper()}_API_KEY"):
             # Fallback for providers where API key might be named like PROVIDER_API_KEY but not explicitly handled above
            api_key = os.getenv(f"{provider.upper()}_API_KEY")

        if not api_key and provider not in ["ollama"]:
            chat_bp_logger.error(f"API key for {provider} is not set. Looked for {provider.upper()}_API_KEY.")
            return jsonify({'status': 'error', 'message': f'API key for {provider} not configured on server.'}), 500
        
        messages_for_llm = [{"role": "user", "content": prompt}]
        
        chat_bp_logger.info(f"Sending request to LiteLLM with model: {model_name_for_api} for provider {provider}")

        response = completion(
            model=model_name_for_api, #This should be like "gpt-3.5-turbo" or "ollama/llama2" or "gemini/gemini-pro"
            messages=messages_for_llm,
            api_key=api_key, # Pass None if not needed (e.g. Ollama)
            # For some providers like Azure, custom_llm_provider might be needed.
            # LiteLLM usually infers provider from model name string.
        )
        
        advice = response.choices[0].message.content
        chat_bp_logger.info(f"Received advice from LLM: {advice[:100]}...")
        return jsonify({'status': 'success', 'advice': advice})

    except NameError as e: # Specifically catch if 'completion' is not defined
        if 'completion' in str(e):
            chat_bp_logger.error("LiteLLM is likely not installed. Please add 'litellm' to requirements.txt and install it.")
            return jsonify({'status': 'error', 'message': 'LLM integration library (LiteLLM) not available on server. Please install dependencies.'}), 500
        else:
            error_msg = f"Unexpected NameError: {str(e)}"
            chat_bp_logger.error(error_msg)
            return jsonify({'status': 'error', 'message': error_msg}), 500
    except Exception as e:
        error_msg = f"Error calling LLM: {str(e)}"
        chat_bp_logger.error(error_msg, exc_info=True) # Log full traceback for other errors
        return jsonify({'status': 'error', 'message': error_msg}), 500 